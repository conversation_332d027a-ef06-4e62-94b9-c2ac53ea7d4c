import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, Button, Spin, Space } from 'antd';
import moment from 'moment';
import assessmentApi from '@/service/assessmentApi';
import systemApi from '@/service/system';
import type {
  ApiResponse,
  AssessmentPersonnel,
  AssessmentPersonnelSaveParams,
} from '@/service/assessmentApi';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj?: AssessmentPersonnel;
  closeModal: () => void;
  mode: 'add' | 'edit' | 'view';
};

/**
 * @description 考核人员安排 新增、编辑、查看弹窗
 * @returns React.FC
 */
const AssessmentPersonnelModal: React.FC<PropsTypes> = ({
  dataObj,
  closeModal = () => {},
  mode = 'add',
}) => {
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  /**
   * 是否为只读模式
   */
  const isReadOnly: boolean = mode === 'view';

  useEffect(() => {
    if (dataObj && dataObj.id) {
      // 编辑或查看模式，设置表单值
      form.setValues({
        ...dataObj,
        assessmentTime: [dataObj.startTime, dataObj.endTime],
      });
    } else {
      // 新增模式，设置默认值
      form.setValues({
        assessmentStatus: '1', // 默认待开始
        assessmentTime: [
          moment().format('YYYY-MM-DD HH:mm:ss'),
          moment().add(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
      });
    }
  }, [dataObj, form]);

  /**
   * 取消操作
   */
  const handleCancel: () => void = () => {
    form.reset();
    closeModal();
  };

  /**
   * 保存操作
   */
  const handleSave: () => Promise<void> = async () => {
    try {
      setIsSaving(true);

      // 表单验证
      await form.validate();
      const formValues: Record<string, unknown> = form.values;

      // 构造保存参数
      const saveParams: AssessmentPersonnelSaveParams = {
        id: dataObj?.id,
        assessmentName: formValues.assessmentName as string,
        assessmentType: formValues.assessmentType as string,
        startTime: (formValues.assessmentTime as string[])[0],
        endTime: (formValues.assessmentTime as string[])[1],
        description: formValues.description as string,
        unitId: formValues.unitId as string,
        personnelList: [], // 暂时为空，后续可扩展
      };

      const result: ApiResponse<boolean> = await assessmentApi.saveAssessmentPersonnel(saveParams);

      if (result.code === 200) {
        message.success(mode === 'add' ? '新增成功' : '编辑成功');
        closeModal();
      } else {
        message.error(result.msg || '保存失败');
      }
    } catch (error) {
      message.error('保存失败，请检查输入信息');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div>
      <Spin spinning={false}>
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="assessmentName"
            title="考核名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入考核名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="assessmentType"
            title="考核类型"
            labelType={1}
            required
            componentName="Selector"
            componentProps={{
              request: async () => {
                return [
                  { code: '1', text: '技能考核' },
                  { code: '2', text: '安全考核' },
                  { code: '3', text: '综合考核' },
                  { code: '4', text: '专项考核' },
                ];
              },
              p_props: {
                placeholder: '请选择考核类型',
                disabled: isReadOnly,
              },
            }}
          />

          <YTHForm.Item
            name="unitId"
            title="组织机构"
            labelType={1}
            required
            componentName="Selector"
            componentProps={{
              request: async () => {
                const result: any = await systemApi.unitTree();
                return result ? [result] : [];
              },
              p_props: {
                placeholder: '请选择组织机构',
                disabled: isReadOnly,
              },
            }}
          />

          <YTHForm.Item
            name="assessmentTime"
            title="考核时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
              placeholder: ['开始时间', '结束时间'],
              disabled: isReadOnly,
              mode: 'range',
            }}
          />

          <YTHForm.Item
            name="description"
            title="考核描述"
            labelType={1}
            mergeRow={1}
            componentName="Input"
            componentProps={{
              placeholder: '请输入考核描述',
              disabled: isReadOnly,
            }}
          />
        </YTHForm>

        {/* 底部按钮 */}
        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>{isReadOnly ? '关闭' : '取消'}</Button>
            {!isReadOnly && (
              <Button type="primary" loading={isSaving} onClick={handleSave}>
                保存
              </Button>
            )}
          </Space>
        </div>
      </Spin>
    </div>
  );
};

export default AssessmentPersonnelModal;
